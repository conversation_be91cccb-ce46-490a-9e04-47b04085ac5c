'use client';

import axios from 'axios';
import { isDemoMode } from '../config/demoMode';

/**
 * Place an order using the Zerodha API
 * @param params Order parameters
 * @returns Response from the Zerodha API
 */
export const placeOrder = async (params: {
  exchange: string;
  tradingsymbol: string;
  transaction_type: 'BUY' | 'SELL';
  quantity: number;
  price?: number;
  product: 'CNC' | 'MIS' | 'NRML';
  order_type: 'MARKET' | 'LIMIT' | 'SL' | 'SL-M';
  validity: 'DAY' | 'IOC';
  disclosed_quantity?: number;
  trigger_price?: number;
  squareoff?: number;
  stoploss?: number;
  trailing_stoploss?: number;
  tag?: string;
}) => {
  try {
    // Get access token from localStorage
    const accessToken = localStorage.getItem('zerodha_access_token');
    if (!accessToken) {
      throw new Error('No access token found. Please connect to Zerodha first.');
    }

    // Use our proxy endpoint to avoid CORS issues
    const response = await axios.post('/api/zerodha/proxy', {
      endpoint: 'orders/regular',
      method: 'POST',
      data: params,
      accessToken,
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to place order');
    }

    return response.data.data;
  } catch (error: any) {
    console.error('Error placing order:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Get order history
 * @returns Order history from Zerodha API
 */
export const getOrders = async () => {
  try {
    // Get access token from localStorage
    const accessToken = localStorage.getItem('zerodha_access_token');
    if (!accessToken) {
      throw new Error('No access token found. Please connect to Zerodha first.');
    }

    // Use our proxy endpoint to avoid CORS issues
    const response = await axios.post('/api/zerodha/proxy', {
      endpoint: 'orders',
      method: 'GET',
      accessToken,
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get orders');
    }

    return response.data.data;
  } catch (error: any) {
    console.error('Error getting orders:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Check if user is connected to Zerodha (or in demo mode)
 * @returns Boolean indicating if user is connected
 */
export const isConnectedToZerodha = (): boolean => {
  if (typeof window === 'undefined') return false;

  // In demo mode, always return true if user is authenticated
  if (isDemoMode()) {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        return user.id ? true : false; // Return true if user exists
      } catch (e) {
        console.error('Error parsing user from localStorage:', e);
      }
    }
    return false;
  }

  // Real mode: Check for actual Zerodha token
  // Check for token in localStorage directly
  const accessToken = localStorage.getItem('zerodha_access_token');
  if (accessToken) return true;

  // Check for token in user object
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      const user = JSON.parse(userStr);
      // Check both token formats
      if (user.zerodhaAccessToken || user.zerodha_access_token) return true;
    } catch (e) {
      console.error('Error parsing user from localStorage:', e);
    }
  }

  return false;
};
