import axios from 'axios';
import { getZerodhaAccessToken } from './tokenManager';

// Zerodha API base URL
const ZERODHA_API_URL = 'https://api.kite.trade';

// Interface for Zerodha API instance
interface ZerodhaCredentials {
  apiKey?: string;
  accessToken?: string;
}

// Interface for trade data
export interface TradeData {
  tradingSymbol: string;
  exchange: string;
  transactionType: 'BUY' | 'SELL';
  quantity: number;
  price?: number;
  product: 'CNC' | 'MIS' | 'NRML';
  orderType: 'MARKET' | 'LIMIT';
  validity: 'DAY' | 'IOC';
}

// Class for interacting with Zerodha API
export class ZerodhaAPI {
  private credentials: ZerodhaCredentials;

  constructor() {
    this.credentials = {};

    // Get API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;
    if (apiKey) {
      this.credentials.apiKey = apiKey;
    }
  }

  // Get user profile
  async getProfile(): Promise<any> {
    try {
      // Get access token if not already set
      if (!this.credentials.accessToken) {
        this.credentials.accessToken = await getZerodhaAccessToken();
      }

      // Check if we have the required credentials
      if (!this.credentials.apiKey || !this.credentials.accessToken) {
        throw new Error('API key or access token not available');
      }

      const response = await axios.get(`${ZERODHA_API_URL}/user/profile`, {
        headers: {
          'X-Kite-Version': '3',
          Authorization: `token ${this.credentials.apiKey}:${this.credentials.accessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching Zerodha profile:', error);
      throw new Error('Failed to fetch Zerodha profile');
    }
  }

  // Place an order
  async placeOrder(tradeData: TradeData): Promise<string> {
    try {
      if (!this.credentials.accessToken) {
        throw new Error('Access token not available');
      }

      const response = await axios.post(
        `${ZERODHA_API_URL}/orders/regular`,
        {
          exchange: tradeData.exchange,
          tradingsymbol: tradeData.tradingSymbol,
          transaction_type: tradeData.transactionType,
          quantity: tradeData.quantity,
          price: tradeData.price,
          product: tradeData.product,
          order_type: tradeData.orderType,
          validity: tradeData.validity,
        },
        {
          headers: {
            'X-Kite-Version': '3',
            Authorization: `token ${this.credentials.apiKey}:${this.credentials.accessToken}`,
          },
        }
      );

      return response.data.order_id;
    } catch (error) {
      console.error('Error placing Zerodha order:', error);
      throw new Error('Failed to place Zerodha order');
    }
  }

  // Get orders
  async getOrders(): Promise<any[]> {
    try {
      if (!this.credentials.accessToken) {
        throw new Error('Access token not available');
      }

      const response = await axios.get(`${ZERODHA_API_URL}/orders`, {
        headers: {
          'X-Kite-Version': '3',
          Authorization: `token ${this.credentials.apiKey}:${this.credentials.accessToken}`,
        },
      });

      return response.data.orders;
    } catch (error) {
      console.error('Error fetching Zerodha orders:', error);
      throw new Error('Failed to fetch Zerodha orders');
    }
  }

  // Get positions
  async getPositions(): Promise<any> {
    try {
      if (!this.credentials.accessToken) {
        throw new Error('Access token not available');
      }

      const response = await axios.get(`${ZERODHA_API_URL}/portfolio/positions`, {
        headers: {
          'X-Kite-Version': '3',
          Authorization: `token ${this.credentials.apiKey}:${this.credentials.accessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching Zerodha positions:', error);
      throw new Error('Failed to fetch Zerodha positions');
    }
  }
}

// Function to get Zerodha login URL
export const getZerodhaLoginUrl = (apiKey: string, redirectUri: string): string => {
  return `https://kite.zerodha.com/connect/login?api_key=${apiKey}&redirect_uri=${encodeURIComponent(redirectUri)}`;
};

export default ZerodhaAPI;
