import { prisma } from '@/lib/prisma'
import { Notification, NotificationType } from '@prisma/client'

export interface CreateNotificationData {
  userId: string
  type: NotificationType
  title: string
  message: string
  data?: any
}

export class NotificationService {
  // Create a new notification
  static async createNotification(data: CreateNotificationData): Promise<Notification> {
    return prisma.notification.create({
      data: {
        userId: data.userId,
        type: data.type,
        title: data.title,
        message: data.message,
        data: data.data || null,
      },
    })
  }

  // Get notifications for a user
  static async getUserNotifications(
    userId: string,
    limit?: number,
    unreadOnly?: boolean
  ): Promise<Notification[]> {
    return prisma.notification.findMany({
      where: {
        userId,
        ...(unreadOnly && { isRead: false }),
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    })
  }

  // Mark notification as read
  static async markAsRead(notificationId: string): Promise<Notification> {
    return prisma.notification.update({
      where: { id: notificationId },
      data: { isRead: true },
    })
  }

  // Mark all notifications as read for a user
  static async markAllAsRead(userId: string): Promise<number> {
    const result = await prisma.notification.updateMany({
      where: {
        userId,
        isRead: false,
      },
      data: { isRead: true },
    })

    return result.count
  }

  // Get unread count
  static async getUnreadCount(userId: string): Promise<number> {
    return prisma.notification.count({
      where: {
        userId,
        isRead: false,
      },
    })
  }

  // Delete notification
  static async deleteNotification(notificationId: string): Promise<void> {
    await prisma.notification.delete({
      where: { id: notificationId },
    })
  }

  // Delete old notifications (cleanup)
  static async deleteOldNotifications(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)

    const result = await prisma.notification.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
        isRead: true,
      },
    })

    return result.count
  }

  // Notification helpers for common scenarios
  static async notifyTradeCopied(
    childUserId: string,
    masterName: string,
    tradeDetails: {
      symbol: string
      transactionType: string
      quantity: number
      price: number
    }
  ): Promise<Notification> {
    return this.createNotification({
      userId: childUserId,
      type: NotificationType.TRADE_COPIED,
      title: 'Trade Copied',
      message: `${masterName} executed a ${tradeDetails.transactionType} order for ${tradeDetails.quantity} shares of ${tradeDetails.symbol} at ₹${tradeDetails.price}`,
      data: tradeDetails,
    })
  }

  static async notifyInvitationSent(
    senderId: string,
    receiverEmail: string
  ): Promise<Notification> {
    return this.createNotification({
      userId: senderId,
      type: NotificationType.INVITATION_SENT,
      title: 'Invitation Sent',
      message: `Invitation sent to ${receiverEmail}`,
      data: { receiverEmail },
    })
  }

  static async notifyInvitationAccepted(
    masterId: string,
    childName: string,
    childEmail: string
  ): Promise<Notification> {
    return this.createNotification({
      userId: masterId,
      type: NotificationType.INVITATION_ACCEPTED,
      title: 'Invitation Accepted',
      message: `${childName} (${childEmail}) has accepted your invitation and joined as a child trader`,
      data: { childName, childEmail },
    })
  }

  static async notifySystemAlert(
    userId: string,
    title: string,
    message: string,
    data?: any
  ): Promise<Notification> {
    return this.createNotification({
      userId,
      type: NotificationType.SYSTEM_ALERT,
      title,
      message,
      data,
    })
  }

  static async notifyError(
    userId: string,
    title: string,
    message: string,
    errorData?: any
  ): Promise<Notification> {
    return this.createNotification({
      userId,
      type: NotificationType.ERROR,
      title,
      message,
      data: errorData,
    })
  }

  // Bulk create notifications
  static async createBulkNotifications(
    notifications: CreateNotificationData[]
  ): Promise<Notification[]> {
    const result = await prisma.notification.createMany({
      data: notifications.map(notif => ({
        userId: notif.userId,
        type: notif.type,
        title: notif.title,
        message: notif.message,
        data: notif.data || null,
      })),
    })

    // Return the created notifications (Prisma createMany doesn't return the records)
    return prisma.notification.findMany({
      where: {
        userId: {
          in: notifications.map(n => n.userId),
        },
      },
      orderBy: { createdAt: 'desc' },
      take: result.count,
    })
  }

  // Get notification statistics
  static async getNotificationStats(userId: string): Promise<{
    total: number
    unread: number
    byType: Record<NotificationType, number>
  }> {
    const notifications = await prisma.notification.findMany({
      where: { userId },
    })

    const total = notifications.length
    const unread = notifications.filter(n => !n.isRead).length

    const byType = notifications.reduce((acc, notification) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1
      return acc
    }, {} as Record<NotificationType, number>)

    return { total, unread, byType }
  }
}
