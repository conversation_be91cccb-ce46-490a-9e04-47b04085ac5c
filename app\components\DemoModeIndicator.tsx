'use client';

import React from 'react';
import { isDemoMode, shouldShowDemoIndicators, getDemoModeMessage } from '../config/demoMode';

interface DemoModeIndicatorProps {
  className?: string;
  variant?: 'banner' | 'badge' | 'card';
}

const DemoModeIndicator: React.FC<DemoModeIndicatorProps> = ({ 
  className = '', 
  variant = 'banner' 
}) => {
  if (!isDemoMode() || !shouldShowDemoIndicators()) {
    return null;
  }

  const demoMessage = getDemoModeMessage();
  
  if (!demoMessage) {
    return null;
  }

  const baseClasses = "flex items-center gap-2 text-sm font-medium";
  
  const variantClasses = {
    banner: "bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded-lg mb-4",
    badge: "bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs",
    card: "bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 text-blue-900 p-4 rounded-xl shadow-sm"
  };

  const iconClasses = {
    banner: "text-blue-600",
    badge: "text-blue-600",
    card: "text-blue-600"
  };

  if (variant === 'banner') {
    return (
      <div className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
        <div className="flex items-center gap-2 flex-1">
          <span className={`text-lg ${iconClasses[variant]}`}>🚀</span>
          <div>
            <div className="font-semibold">{demoMessage.title}</div>
            <div className="text-blue-700 mt-1">{demoMessage.message}</div>
          </div>
        </div>
        <div className="flex items-center gap-2 text-xs text-blue-600">
          <span className="bg-blue-200 px-2 py-1 rounded">DEMO</span>
        </div>
      </div>
    );
  }

  if (variant === 'badge') {
    return (
      <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
        <span className="text-xs">🚀</span>
        <span>DEMO MODE</span>
      </span>
    );
  }

  if (variant === 'card') {
    return (
      <div className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
        <div className="flex items-start gap-3">
          <span className={`text-2xl ${iconClasses[variant]}`}>🚀</span>
          <div className="flex-1">
            <h3 className="font-bold text-lg mb-2">{demoMessage.title}</h3>
            <p className="text-blue-800 mb-3">{demoMessage.message}</p>
            <div className="flex flex-wrap gap-2">
              <span className="bg-blue-200 text-blue-800 px-3 py-1 rounded-full text-xs font-medium">
                No Real Money
              </span>
              <span className="bg-green-200 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
                Safe Testing
              </span>
              <span className="bg-purple-200 text-purple-800 px-3 py-1 rounded-full text-xs font-medium">
                Full Features
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

// Demo mode status component for header/navbar
export const DemoModeStatus: React.FC<{ className?: string }> = ({ className = '' }) => {
  if (!isDemoMode() || !shouldShowDemoIndicators()) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <DemoModeIndicator variant="badge" />
    </div>
  );
};

// Demo mode warning for trading actions
export const DemoModeWarning: React.FC<{ 
  action?: string;
  className?: string;
}> = ({ action = 'action', className = '' }) => {
  if (!isDemoMode()) {
    return null;
  }

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4 ${className}`}>
      <div className="flex items-center gap-2 text-yellow-800">
        <span className="text-lg">⚠️</span>
        <div className="text-sm">
          <span className="font-medium">Demo Mode:</span> This {action} will be simulated. 
          No real trades will be executed.
        </div>
      </div>
    </div>
  );
};

// Demo mode features showcase
export const DemoModeFeatures: React.FC<{ className?: string }> = ({ className = '' }) => {
  if (!isDemoMode()) {
    return null;
  }

  const features = [
    {
      icon: '📊',
      title: 'Realistic Trading',
      description: 'Experience real-time market simulation with live price updates'
    },
    {
      icon: '👥',
      title: 'Master-Child Flow',
      description: 'Test the complete copy trading workflow without real money'
    },
    {
      icon: '📧',
      title: 'Email Invitations',
      description: 'Send and receive actual invitation emails to test the flow'
    },
    {
      icon: '📈',
      title: 'Portfolio Tracking',
      description: 'View detailed portfolio performance and trade history'
    },
    {
      icon: '🔄',
      title: 'Auto Copy Trading',
      description: 'See how trades are automatically copied from master to children'
    },
    {
      icon: '🛡️',
      title: 'Safe Environment',
      description: 'Test all features safely without any financial risk'
    }
  ];

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          🚀 Demo Mode Features
        </h3>
        <p className="text-gray-600">
          Explore all CopyTrade features in a safe, simulated environment
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {features.map((feature, index) => (
          <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
            <span className="text-2xl">{feature.icon}</span>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">{feature.title}</h4>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center gap-2 text-blue-800">
          <span className="text-lg">💡</span>
          <div className="text-sm">
            <span className="font-medium">Ready for Production:</span> When you get the paid Zerodha API, 
            simply disable demo mode in the configuration to switch to real trading.
          </div>
        </div>
      </div>
    </div>
  );
};

export default DemoModeIndicator;
