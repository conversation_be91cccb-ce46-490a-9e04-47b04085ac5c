'use client';

import Link from 'next/link';
import { Separator } from '../../../components/ui/separator';
import { Badge } from '../../../components/ui/badge';
import { 
  TrendingUp, 
  Mail, 
  Twitter, 
  Linkedin, 
  Github,
  Shield,
  Zap,
  Users
} from 'lucide-react';

const footerLinks = {
  product: [
    { name: 'Features', href: '#features' },
    { name: 'Pricing', href: '#pricing' },
    { name: 'API Documentation', href: '/docs' },
    { name: 'Integrations', href: '/integrations' },
    { name: 'Security', href: '/security' },
    { name: 'Roadmap', href: '/roadmap' }
  ],
  platform: [
    { name: 'Master Dashboard', href: '/master/dashboard' },
    { name: 'Child Dashboard', href: '/child/dashboard' },
    { name: 'Analytics', href: '/analytics' },
    { name: 'Risk Management', href: '/risk' },
    { name: 'Mobile App', href: '/mobile' },
    { name: 'Desktop App', href: '/desktop' }
  ],
  support: [
    { name: 'Help Center', href: '/help' },
    { name: 'Community Forum', href: '/community' },
    { name: 'Contact Support', href: '/support' },
    { name: 'Status Page', href: '/status' },
    { name: 'Bug Reports', href: '/bugs' },
    { name: 'Feature Requests', href: '/features' }
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Blog', href: '/blog' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press Kit', href: '/press' },
    { name: 'Partners', href: '/partners' },
    { name: 'Investors', href: '/investors' }
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'GDPR', href: '/gdpr' },
    { name: 'Compliance', href: '/compliance' },
    { name: 'Licenses', href: '/licenses' }
  ]
};

const socialLinks = [
  { name: 'Twitter', href: '#', icon: Twitter },
  { name: 'LinkedIn', href: '#', icon: Linkedin },
  { name: 'GitHub', href: '#', icon: Github },
  { name: 'Email', href: 'mailto:<EMAIL>', icon: Mail }
];

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Main footer content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="w-8 h-8 text-blue-400" />
              <span className="text-2xl font-bold">CopyTrade</span>
            </div>
            <p className="text-gray-400 mb-6 max-w-sm">
              The AI-powered trading platform that helps you scale your trading strategies 
              with intelligent copy trading and risk management.
            </p>
            
            {/* Trust badges */}
            <div className="flex flex-wrap gap-2 mb-6">
              <Badge variant="outline" className="border-gray-600 text-gray-300">
                <Shield className="w-3 h-3 mr-1" />
                Bank-level Security
              </Badge>
              <Badge variant="outline" className="border-gray-600 text-gray-300">
                <Zap className="w-3 h-3 mr-1" />
                Real-time Execution
              </Badge>
              <Badge variant="outline" className="border-gray-600 text-gray-300">
                <Users className="w-3 h-3 mr-1" />
                10,000+ Traders
              </Badge>
            </div>

            {/* Social links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <Link
                  key={social.name}
                  href={social.href}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <social.icon className="w-5 h-5" />
                  <span className="sr-only">{social.name}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Product links */}
          <div>
            <h3 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
              Product
            </h3>
            <ul className="space-y-3">
              {footerLinks.product.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Platform links */}
          <div>
            <h3 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
              Platform
            </h3>
            <ul className="space-y-3">
              {footerLinks.platform.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support links */}
          <div>
            <h3 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
              Support
            </h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company links */}
          <div>
            <h3 className="text-sm font-semibold text-white uppercase tracking-wider mb-4">
              Company
            </h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <Separator className="my-8 bg-gray-700" />

        {/* Bottom footer */}
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex flex-wrap items-center space-x-6 text-sm text-gray-400">
            <span>© 2024 CopyTrade, Inc.</span>
            {footerLinks.legal.map((link) => (
              <Link
                key={link.name}
                href={link.href}
                className="hover:text-white transition-colors"
              >
                {link.name}
              </Link>
            ))}
          </div>
          
          <div className="mt-4 md:mt-0 text-sm text-gray-400">
            Made with ❤️ for traders worldwide
          </div>
        </div>
      </div>
    </footer>
  );
}
