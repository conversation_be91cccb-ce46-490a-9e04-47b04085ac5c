'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';
import Header from '@/app/components/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Input from '@/app/components/Input';
import ZerodhaStatus from '@/app/components/ZerodhaStatus';
import OrderForm from '@/app/components/OrderForm';
import { isConnectedToZerodha } from '@/app/utils/orderUtils';
import DemoModeIndicator, { DemoModeStatus } from '@/app/components/DemoModeIndicator';
import { isDemoMode } from '@/app/config/demoMode';
import { motion } from 'framer-motion';
import { GlowingBorder } from '@/app/components/ui/animated-border';
import { Users, TrendingUp, Mail, CheckCircle, AlertCircle, Rocket } from 'lucide-react';

export default function MasterDashboardPage() {
  const [childEmail, setChildEmail] = useState('');
  const [isInviting, setIsInviting] = useState(false);
  const [inviteSuccess, setInviteSuccess] = useState(false);
  const [inviteError, setInviteError] = useState('');
  const [childUsers, setChildUsers] = useState<any[]>([]);
  const [loadingChildren, setLoadingChildren] = useState(false);

  const router = useRouter();
  const { user, inviteChild, connectToZerodha, getChildUsers } = useAuth();

  // Redirect if not authenticated or not a master
  useEffect(() => {
    if (user && user.role !== 'master') {
      router.push('/child/dashboard');
    }
  }, [user, router]);

  // Load child users when component mounts
  useEffect(() => {
    const loadChildUsers = async () => {
      if (user && user.role === 'master') {
        setLoadingChildren(true);
        try {
          const children = await getChildUsers();
          setChildUsers(children);
        } catch (error) {
          console.error('Failed to load child users:', error);
        } finally {
          setLoadingChildren(false);
        }
      }
    };

    loadChildUsers();
  }, [user, getChildUsers]);

  // Check if Zerodha is connected using our utility function
  const isZerodhaConnected = isConnectedToZerodha();

  const handleInviteChild = async (e: React.FormEvent) => {
    e.preventDefault();
    setInviteSuccess(false);
    setInviteError('');
    setIsInviting(true);

    try {
      await inviteChild(childEmail);
      setInviteSuccess(true);
      setChildEmail('');

      // Reload child users after successful invitation
      const children = await getChildUsers();
      setChildUsers(children);
    } catch (error) {
      console.error('Invitation error:', error);
      setInviteError('Failed to send invitation. Please try again.');
    } finally {
      setIsInviting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Animated background gradient */}
      <div className="fixed inset-0 bg-gradient-to-br from-blue-50/50 via-background to-purple-50/50 dark:from-blue-950/10 dark:via-background dark:to-purple-950/10" />

      {/* Animated gradient overlay */}
      <motion.div
        className="fixed inset-0 opacity-30 dark:opacity-20 pointer-events-none"
        animate={{
          background: [
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 50% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)"
          ]
        }}
        transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
      />

      {/* Floating orbs */}
      <motion.div
        className="fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/30 to-purple-400/30 dark:from-blue-400/20 dark:to-purple-400/20 rounded-full blur-xl pointer-events-none"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
      />
      <motion.div
        className="fixed top-40 right-20 w-24 h-24 bg-gradient-to-r from-purple-400/30 to-pink-400/30 dark:from-purple-400/20 dark:to-pink-400/20 rounded-full blur-xl pointer-events-none"
        animate={{
          y: [0, 20, 0],
          x: [0, -15, 0],
        }}
        transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
      />

      <Header />
      <main className="flex-grow relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            className="flex items-center justify-between mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.h1
              className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Master Dashboard
            </motion.h1>
            <DemoModeStatus />
          </motion.div>

          {/* Demo Mode Indicator */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <DemoModeIndicator variant="banner" />
          </motion.div>

          {/* Zerodha Status Component */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <ZerodhaStatus />
          </motion.div>

          {/* Child Users Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mb-8"
          >
            <GlowingBorder className="h-full" glowColor="blue">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Users className="w-6 h-6 text-blue-600" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">Child Users</CardTitle>
                      <CardDescription className="text-muted-foreground">
                        Invite users to copy your trades
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Invite Form */}
                  <form onSubmit={handleInviteChild} className="mb-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                      <div className="flex-grow">
                        <Input
                          type="email"
                          placeholder="Enter email address"
                          value={childEmail}
                          onChange={(e) => setChildEmail(e.target.value)}
                          required
                          disabled={!isZerodhaConnected}
                          fullWidth
                          className="bg-background/50 border-2 border-transparent bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-cyan-500/10 focus:from-blue-500/20 focus:via-purple-500/20 focus:to-cyan-500/20 transition-all duration-300"
                        />
                      </div>
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          type="submit"
                          disabled={isInviting || !isZerodhaConnected}
                          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                        >
                          {isInviting ? (
                            <div className="flex items-center">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Sending...
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <Mail className="w-4 h-4 mr-2" />
                              Send Invitation
                            </div>
                          )}
                        </Button>
                      </motion.div>
                    </div>

                    {inviteSuccess && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-200 rounded-lg"
                      >
                        <div className="flex items-center text-green-700 dark:text-green-400">
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Invitation sent successfully!
                        </div>
                      </motion.div>
                    )}

                    {inviteError && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-3 bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-200 rounded-lg"
                      >
                        <div className="flex items-center text-red-700 dark:text-red-400">
                          <AlertCircle className="w-4 h-4 mr-2" />
                          {inviteError}
                        </div>
                      </motion.div>
                    )}

                    {/* Only show warning if not connected to Zerodha */}
                    {!isZerodhaConnected && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-3 bg-gradient-to-r from-amber-500/10 to-yellow-500/10 border border-amber-200 rounded-lg"
                      >
                        <div className="flex items-center text-amber-700 dark:text-amber-400">
                          <AlertCircle className="w-4 h-4 mr-2" />
                          You need to connect your Zerodha account before inviting child users.
                        </div>
                      </motion.div>
                    )}
                  </form>

                  {/* Child Users List */}
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                      <Users className="w-5 h-5 mr-2 text-blue-600" />
                      Connected Child Users
                    </h3>

                    {loadingChildren ? (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg text-center"
                      >
                        <div className="flex justify-center items-center text-muted-foreground">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-2"></div>
                          Loading child users...
                        </div>
                      </motion.div>
                    ) : childUsers.length > 0 ? (
                      <div className="space-y-3">
                        {childUsers.map((child, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: index * 0.1 }}
                          >
                            <GlowingBorder glowColor={index % 2 === 0 ? "purple" : "cyan"}>
                              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                                <CardContent className="p-4">
                                  <div className="flex items-center justify-between">
                                    <div className="space-y-1">
                                      <p className="font-medium text-foreground">{child.childEmail}</p>
                                      <p className="text-sm text-muted-foreground">
                                        Zerodha ID: {child.zerodhaUserId}
                                      </p>
                                      <p className="text-xs text-muted-foreground">
                                        Connected: {new Date(child.connectedAt).toLocaleDateString()}
                                      </p>
                                    </div>
                                    <div className="flex items-center">
                                      <motion.div
                                        initial={{ scale: 0 }}
                                        animate={{ scale: 1 }}
                                        transition={{ delay: 0.5 + index * 0.1, type: "spring" }}
                                      >
                                        <Badge className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-300/50 text-green-700 dark:text-green-400">
                                          <CheckCircle className="w-3 h-3 mr-1" />
                                          Connected
                                        </Badge>
                                      </motion.div>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            </GlowingBorder>
                          </motion.div>
                        ))}
                      </div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="p-6 bg-gradient-to-r from-gray-500/10 to-slate-500/10 rounded-lg text-center"
                      >
                        <div className="text-muted-foreground">
                          <Users className="w-8 h-8 mx-auto mb-2 opacity-50" />
                          No child users connected yet. Send an invitation to get started!
                        </div>
                      </motion.div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>

          {/* Order Form Section */}
          {isConnectedToZerodha() && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
              className="mb-8"
            >
              <OrderForm />
            </motion.div>
          )}

          {/* Demo Trading Section */}
          {isDemoMode() && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="mb-8"
            >
              <GlowingBorder className="h-full" glowColor="cyan">
                <Card className="border-0 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <motion.div
                          className="p-3 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg"
                          whileHover={{ scale: 1.1, rotate: 5 }}
                          whileTap={{ scale: 0.95 }}
                          animate={{ rotate: [0, 5, -5, 0] }}
                          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                        >
                          <Rocket className="w-6 h-6 text-blue-600" />
                        </motion.div>
                        <div>
                          <h2 className="text-xl font-semibold text-foreground mb-2 flex items-center">
                            🚀 Try Demo Trading
                          </h2>
                          <p className="text-muted-foreground">
                            Experience the full trading workflow with realistic market simulation.
                            Perfect for testing the copy trading functionality!
                          </p>
                        </div>
                      </div>
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Link href="/demo/trading">
                          <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 transition-all duration-300">
                            <Rocket className="w-4 h-4 mr-2" />
                            Open Demo Trading
                          </Button>
                        </Link>
                      </motion.div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>
          )}

          {/* Recent Trades Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.4 }}
          >
            <GlowingBorder className="h-full" glowColor="pink">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <TrendingUp className="w-6 h-6 text-pink-600" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">Recent Trades</CardTitle>
                      <CardDescription className="text-muted-foreground">
                        Your recent trades that were copied to child accounts
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Mock data - in a real app, this would come from the backend */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="p-6 bg-gradient-to-r from-gray-500/10 to-slate-500/10 rounded-lg text-center"
                  >
                    <div className="text-muted-foreground">
                      <TrendingUp className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      No trades executed yet
                    </div>
                  </motion.div>
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>
        </div>
      </main>
    </div>
  );
}
