'use client';

import axios from 'axios';

/**
 * Refreshes the Zerodha access token using the refresh token
 * @returns A promise that resolves to the new access token
 */
export const refreshZerodhaToken = async (): Promise<string> => {
  try {
    // Get refresh token from localStorage
    const refreshToken = localStorage.getItem('zerodhaRefreshToken');
    
    if (!refreshToken) {
      throw new Error('No refresh token found');
    }
    
    // Call the API to refresh the token
    const response = await axios.put('/api/zerodha', {
      refreshToken
    });
    
    if (response.data.success) {
      // Get user from localStorage
      const userStr = localStorage.getItem('user');
      
      if (!userStr) {
        throw new Error('User not found in localStorage');
      }
      
      const user = JSON.parse(userStr);
      
      // Update user with new access token
      const updatedUser = {
        ...user,
        zerodhaAccessToken: response.data.accessToken,
      };
      
      // Store updated user in localStorage
      localStorage.setItem('user', JSON.stringify(updatedUser));
      
      // Return the new access token
      return response.data.accessToken;
    } else {
      throw new Error('Failed to refresh token');
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};

/**
 * Gets the current Zerodha access token, refreshing it if necessary
 * @returns A promise that resolves to the access token
 */
export const getZerodhaAccessToken = async (): Promise<string> => {
  try {
    // Get user from localStorage
    const userStr = localStorage.getItem('user');
    
    if (!userStr) {
      throw new Error('User not found in localStorage');
    }
    
    const user = JSON.parse(userStr);
    
    // Check if access token exists
    if (!user.zerodhaAccessToken) {
      throw new Error('No access token found');
    }
    
    // In a real app, you would check if the token is expired
    // For now, we'll just return the existing token
    return user.zerodhaAccessToken;
  } catch (error) {
    console.error('Error getting access token:', error);
    
    // Try to refresh the token
    return refreshZerodhaToken();
  }
};
