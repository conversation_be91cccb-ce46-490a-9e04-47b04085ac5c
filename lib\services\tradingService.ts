import { prisma } from '@/lib/prisma'
import { Trade, Order, TradeStatus, TransactionType, OrderType, ProductType } from '@prisma/client'
import { Decimal } from '@prisma/client/runtime/library'

export interface CreateTradeData {
  userId: string
  symbol: string
  exchange: string
  transactionType: TransactionType
  quantity: number
  price: number
  orderType: OrderType
  productType: ProductType
  zerodhaOrderId?: string
  isDemo?: boolean
}

export interface CreateOrderData {
  userId: string
  symbol: string
  exchange: string
  transactionType: TransactionType
  quantity: number
  price?: number
  triggerPrice?: number
  orderType: OrderType
  productType: ProductType
  validity?: string
  zerodhaOrderId?: string
  isDemo?: boolean
}

export class TradingService {
  // Create a new trade
  static async createTrade(data: CreateTradeData): Promise<Trade> {
    return prisma.trade.create({
      data: {
        userId: data.userId,
        symbol: data.symbol,
        exchange: data.exchange,
        transactionType: data.transactionType,
        quantity: data.quantity,
        price: new Decimal(data.price),
        orderType: data.orderType,
        productType: data.productType,
        zerodhaOrderId: data.zerodhaOrderId,
        isDemo: data.isDemo || false,
        status: TradeStatus.PENDING,
      },
    })
  }

  // Update trade status
  static async updateTradeStatus(
    tradeId: string,
    status: TradeStatus,
    executedAt?: Date
  ): Promise<Trade> {
    return prisma.trade.update({
      where: { id: tradeId },
      data: {
        status,
        executedAt: executedAt || (status === TradeStatus.COMPLETE ? new Date() : undefined),
      },
    })
  }

  // Get trades for a user
  static async getUserTrades(userId: string, limit?: number): Promise<Trade[]> {
    return prisma.trade.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    })
  }

  // Get trades by status
  static async getTradesByStatus(userId: string, status: TradeStatus): Promise<Trade[]> {
    return prisma.trade.findMany({
      where: {
        userId,
        status,
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  // Create a new order
  static async createOrder(data: CreateOrderData): Promise<Order> {
    return prisma.order.create({
      data: {
        userId: data.userId,
        symbol: data.symbol,
        exchange: data.exchange,
        transactionType: data.transactionType,
        quantity: data.quantity,
        price: data.price ? new Decimal(data.price) : null,
        triggerPrice: data.triggerPrice ? new Decimal(data.triggerPrice) : null,
        orderType: data.orderType,
        productType: data.productType,
        validity: data.validity || 'DAY',
        pendingQuantity: data.quantity,
        zerodhaOrderId: data.zerodhaOrderId,
        status: 'OPEN',
        isDemo: data.isDemo || false,
      },
    })
  }

  // Update order
  static async updateOrder(
    orderId: string,
    data: {
      status?: string
      averagePrice?: number
      filledQuantity?: number
      pendingQuantity?: number
      cancelledQuantity?: number
      statusMessage?: string
      orderTimestamp?: Date
      exchangeTimestamp?: Date
    }
  ): Promise<Order> {
    const updateData: any = { ...data }
    
    if (data.averagePrice !== undefined) {
      updateData.averagePrice = new Decimal(data.averagePrice)
    }

    return prisma.order.update({
      where: { id: orderId },
      data: updateData,
    })
  }

  // Get orders for a user
  static async getUserOrders(userId: string, limit?: number): Promise<Order[]> {
    return prisma.order.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    })
  }

  // Get pending orders
  static async getPendingOrders(userId: string): Promise<Order[]> {
    return prisma.order.findMany({
      where: {
        userId,
        status: 'OPEN',
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  // Copy trade to children
  static async copyTradeToChildren(
    originalTradeId: string,
    childUserIds: string[]
  ): Promise<{ success: string[]; failed: { userId: string; error: string }[] }> {
    const originalTrade = await prisma.trade.findUnique({
      where: { id: originalTradeId },
    })

    if (!originalTrade) {
      throw new Error('Original trade not found')
    }

    const success: string[] = []
    const failed: { userId: string; error: string }[] = []

    for (const childUserId of childUserIds) {
      try {
        // Create copied trade
        const copiedTrade = await this.createTrade({
          userId: childUserId,
          symbol: originalTrade.symbol,
          exchange: originalTrade.exchange,
          transactionType: originalTrade.transactionType,
          quantity: originalTrade.quantity,
          price: originalTrade.price.toNumber(),
          orderType: originalTrade.orderType,
          productType: originalTrade.productType,
          isDemo: originalTrade.isDemo,
        })

        // Get the relationship
        const relationship = await prisma.masterChildRelationship.findFirst({
          where: {
            masterId: originalTrade.userId,
            childId: childUserId,
            isActive: true,
          },
        })

        if (relationship) {
          // Create trade copy record
          await prisma.tradeCopy.create({
            data: {
              relationshipId: relationship.id,
              originalTradeId: originalTrade.id,
              copiedTradeId: copiedTrade.id,
              status: 'SUCCESS',
            },
          })
        }

        success.push(childUserId)
      } catch (error) {
        failed.push({
          userId: childUserId,
          error: error instanceof Error ? error.message : 'Unknown error',
        })
      }
    }

    return { success, failed }
  }

  // Get trade copy history
  static async getTradeCopyHistory(userId: string): Promise<any[]> {
    return prisma.tradeCopy.findMany({
      where: {
        OR: [
          { originalTrade: { userId } },
          { copiedTrade: { userId } },
        ],
      },
      include: {
        originalTrade: true,
        copiedTrade: true,
        relationship: {
          include: {
            master: true,
            child: true,
          },
        },
      },
      orderBy: { copiedAt: 'desc' },
    })
  }

  // Get trading statistics
  static async getTradingStats(userId: string): Promise<{
    totalTrades: number
    completedTrades: number
    pendingTrades: number
    totalVolume: number
    totalPnL: number
  }> {
    const trades = await prisma.trade.findMany({
      where: { userId },
    })

    const totalTrades = trades.length
    const completedTrades = trades.filter(t => t.status === TradeStatus.COMPLETE).length
    const pendingTrades = trades.filter(t => t.status === TradeStatus.PENDING).length
    const totalVolume = trades.reduce((sum, trade) => sum + (trade.price.toNumber() * trade.quantity), 0)

    // Calculate P&L (simplified - would need more complex logic in real app)
    const totalPnL = 0 // This would require portfolio calculations

    return {
      totalTrades,
      completedTrades,
      pendingTrades,
      totalVolume,
      totalPnL,
    }
  }
}
