'use client';

import React, { useEffect, useState } from 'react';
import { ZerodhaApi } from '../utils/zerodhaApi';

/**
 * Component to display Zerodha connection status and user profile
 */
export default function ZerodhaStatus() {
  const [isConnected, setIsConnected] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Check if access token exists in localStorage or in user object
    const accessToken = localStorage.getItem('zerodha_access_token');
    const userStr = localStorage.getItem('user');
    const user = userStr ? JSON.parse(userStr) : null;
    const userHasToken = user && user.zerodhaAccessToken;

    setIsConnected(!!(accessToken || userHasToken));

    // If connected, fetch profile
    if (accessToken || userHasToken) {
      fetchProfile();
    }
  }, []);

  const fetchProfile = async () => {
    setLoading(true);
    setError(null);

    try {
      const api = new ZerodhaApi();
      const profileData = await api.getProfile();
      console.log('Profile data:', profileData);
      setProfile(profileData);
    } catch (err: any) {
      console.error('Error fetching profile:', err);
      // For MVP, just show a generic message and don't treat this as a critical error
      setError('Could not fetch profile details. This is not critical for the application to function.');
    } finally {
      setLoading(false);
    }
  };

  const connectToZerodha = () => {
    // Get API key from environment variable
    const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;

    if (!apiKey) {
      setError('Zerodha API key not found in environment variables');
      return;
    }

    // Redirect to Zerodha login page
    window.location.href = `https://kite.zerodha.com/connect/login?v=3&api_key=${apiKey}`;
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <h2 className="text-xl font-semibold mb-4">Zerodha Connection Status</h2>

      {isConnected ? (
        <div>
          <div className="flex items-center mb-4">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span className="text-green-700 font-medium">Connected to Zerodha</span>
          </div>

          {loading && (
            <div className="flex justify-center my-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
          )}

          {error && (
            <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded mb-4 text-sm">
              {error}
            </div>
          )}

          {profile ? (
            <div className="bg-gray-50 p-4 rounded-lg text-sm">
              <h3 className="font-medium mb-2">User Profile</h3>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">User ID:</div>
                <div>{profile.user_id}</div>
                <div className="text-gray-600">Name:</div>
                <div>{profile.user_name}</div>
                <div className="text-gray-600">Email:</div>
                <div>{profile.email}</div>
              </div>

              <button
                onClick={fetchProfile}
                className="mt-4 px-3 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200"
              >
                Refresh
              </button>
            </div>
          ) : (
            <div className="bg-gray-50 p-4 rounded-lg text-sm">
              <h3 className="font-medium mb-2">Access Token Information</h3>
              <div className="mt-2">
                <div className="text-gray-600 mb-1">Access Token:</div>
                <div className="font-mono text-xs break-all bg-gray-100 p-2 rounded">
                  {localStorage.getItem('zerodha_access_token')?.substring(0, 20)}...
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Your access token is stored and ready to use for placing orders.
                </p>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div>
          <div className="flex items-center mb-4">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
            <span className="text-red-700 font-medium">Not connected to Zerodha</span>
          </div>

          <button
            onClick={connectToZerodha}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Connect to Zerodha
          </button>
        </div>
      )}
    </div>
  );
}
