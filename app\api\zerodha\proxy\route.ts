import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

// Proxy endpoint to avoid CORS issues with Zerodha API
export async function POST(request: NextRequest) {
  try {
    const { endpoint, method, data, accessToken } = await request.json();

    // Validate input
    if (!endpoint || !accessToken) {
      return NextResponse.json(
        { error: 'Endpoint and access token are required' },
        { status: 400 }
      );
    }

    // Get API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;
    
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key not found in environment variables' },
        { status: 500 }
      );
    }

    try {
      // Make request to Zerodha API
      const response = await axios({
        method: method || 'GET',
        url: `https://api.kite.trade/${endpoint}`,
        headers: {
          'X-Kite-Version': '3',
          'Authorization': `token ${apiKey}:${accessToken}`,
          'Content-Type': 'application/json',
        },
        data: data || undefined,
      });

      // Return response data
      return NextResponse.json({
        success: true,
        data: response.data,
      });
    } catch (apiError: any) {
      console.error('Zerodha API error:', apiError.response?.data || apiError.message);
      return NextResponse.json(
        { 
          error: 'Failed to call Zerodha API',
          details: apiError.response?.data || apiError.message
        },
        { status: apiError.response?.status || 500 }
      );
    }
  } catch (error) {
    console.error('Proxy error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
