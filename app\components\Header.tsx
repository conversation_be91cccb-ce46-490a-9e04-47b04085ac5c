'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/app/context/AuthContext';
import Button from './Button';

const Header: React.FC = () => {
  const { user, logout, isAuthenticated } = useAuth();

  return (
    <header className="bg-background shadow-sm border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="text-xl font-bold text-blue-600 dark:text-blue-400">
                CopyTrade
              </Link>
            </div>
            <nav className="ml-6 flex space-x-8">
              {isAuthenticated && (
                <>
                  {user?.role === 'master' ? (
                    <Link href="/master/dashboard" className="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                      Dashboard
                    </Link>
                  ) : (
                    <Link href="/child/dashboard" className="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                      Dashboard
                    </Link>
                  )}
                </>
              )}
            </nav>
          </div>
          <div className="flex items-center">
            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                <span className="text-sm text-foreground">
                  {user?.name} ({user?.role})
                  {user?.email && !user.email.includes('@copytrade.local') && (
                    <span className="ml-2 text-xs text-green-600 dark:text-green-400">✓ Zerodha Connected</span>
                  )}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                >
                  Switch Role
                </Button>
              </div>
            ) : (
              <div className="space-x-4">
                <Link href="/">
                  <Button size="sm">
                    Select Role
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
