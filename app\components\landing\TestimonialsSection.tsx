'use client';

import { Card, CardContent } from '../../../components/ui/card';
import { Badge } from '../../../components/ui/badge';
import { Star, Quote } from 'lucide-react';

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Professional Trader",
    company: "Mumbai",
    content: "CopyTrade has revolutionized my trading business. I can now manage 50+ child accounts effortlessly, and my clients love the transparency and real-time execution.",
    rating: 5,
    avatar: "RK"
  },
  {
    name: "<PERSON><PERSON>",
    role: "Investment Advisor",
    company: "Delhi",
    content: "The AI-powered risk management features have saved me from several potential losses. The platform is intuitive and the support team is incredibly responsive.",
    rating: 5,
    avatar: "PS"
  },
  {
    name: "<PERSON><PERSON>",
    role: "Retail Trader",
    company: "Bangalore",
    content: "As a child trader, I've seen consistent returns by following top masters. The platform makes it easy to diversify across multiple strategies.",
    rating: 5,
    avatar: "AP"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Fintech Entrepreneur",
    company: "Hyderabad",
    content: "We integrated CopyTrade's API into our platform. The documentation is excellent and the integration was seamless. Our users love the copy trading features.",
    rating: 5,
    avatar: "SR"
  },
  {
    name: "Vikram Singh",
    role: "Day Trader",
    company: "Pune",
    content: "The real-time analytics and performance tracking help me optimize my strategies. I've increased my trading efficiency by 300% since using CopyTrade.",
    rating: 5,
    avatar: "VS"
  },
  {
    name: "Kavya Nair",
    role: "Portfolio Manager",
    company: "Chennai",
    content: "Managing multiple client portfolios has never been easier. The white-label solution allowed us to offer copy trading to our clients under our brand.",
    rating: 5,
    avatar: "KN"
  }
];

export default function TestimonialsSection() {
  return (
    <section className="py-24 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            Testimonials
          </Badge>
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100 sm:text-4xl lg:text-5xl">
            Loved by{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              traders worldwide
            </span>
          </h2>
          <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600 dark:text-gray-300">
            Join thousands of traders who have transformed their trading with CopyTrade.
          </p>
        </div>

        {/* Testimonials grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="relative hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                {/* Quote icon */}
                <Quote className="w-8 h-8 text-blue-600 mb-4" />

                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* Content */}
                <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                  "{testimonial.content}"
                </p>

                {/* Author */}
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">{testimonial.name}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {testimonial.role} • {testimonial.company}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats section */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">10,000+</div>
            <div className="text-gray-600 dark:text-gray-400">Active Traders</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">₹500Cr+</div>
            <div className="text-gray-600 dark:text-gray-400">Assets Under Management</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">99.9%</div>
            <div className="text-gray-600 dark:text-gray-400">Uptime</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">24/7</div>
            <div className="text-gray-600 dark:text-gray-400">Support</div>
          </div>
        </div>

        {/* CTA */}
        <div className="mt-16 text-center">
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
            Ready to join our community of successful traders?
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Badge variant="outline" className="px-4 py-2">
              ⭐ 4.9/5 average rating
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              🚀 Join 10,000+ traders
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              💰 Start earning today
            </Badge>
          </div>
        </div>
      </div>
    </section>
  );
}
