/**
 * Utility functions for handling URLs in different environments
 */

/**
 * Get the current app URL based on environment
 * Priority:
 * 1. window.location.origin (client-side)
 * 2. VERCEL_URL (Vercel deployments)
 * 3. NEXT_PUBLIC_SITE_URL (custom deployments)
 * 4. NEXT_PUBLIC_APP_URL (fallback)
 * 5. http://localhost:3000 (development fallback)
 */
export function getAppUrl(): string {
  // Client-side: Use window.location.origin if available
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }

  // Vercel deployment: Use VERCEL_URL
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // Custom deployment: Use site URL or app URL if configured
  if (process.env.NEXT_PUBLIC_SITE_URL) {
    return process.env.NEXT_PUBLIC_SITE_URL;
  }

  if (process.env.NEXT_PUBLIC_APP_URL) {
    return process.env.NEXT_PUBLIC_APP_URL;
  }

  // Development fallback
  return 'http://localhost:3000';
}

/**
 * Server-side function to get app URL from request headers
 * This is more reliable for server-side operations
 */
export function getAppUrlFromRequest(request?: Request): string {
  if (request) {
    const host = request.headers.get('host');
    const protocol = request.headers.get('x-forwarded-proto') || 'http';
    if (host) {
      return `${protocol}://${host}`;
    }
  }

  // Fall back to environment-based detection
  return getAppUrl();
}

/**
 * Get the OAuth callback URL for the current environment
 */
export function getOAuthCallbackUrl(path: string = '/auth/callback'): string {
  return `${getAppUrl()}${path}`;
}

/**
 * Get the Zerodha callback URL for the current environment
 */
export function getZerodhaCallbackUrl(): string {
  return `${getAppUrl()}/auth/zerodha/callback`;
}

/**
 * Get the invitation acceptance URL
 */
export function getInvitationUrl(token: string): string {
  return `${getAppUrl()}/auth/accept-invitation?token=${token}`;
}

