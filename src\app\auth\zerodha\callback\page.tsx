'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Button from '@/app/components/Button';
import axios from 'axios';
import { useAuth } from '@/app/context/AuthContext';

// Loading component for Suspense fallback
function LoadingCallback() {
  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
      <h1 className="text-2xl font-bold mb-6">Zerodha Authentication</h1>
      <div className="flex justify-center mb-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
      <p className="mb-4">Loading Zerodha callback...</p>
    </div>
  );
}

// Main callback component
function ZerodhaCallbackContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing Zerodha authentication...');
  const [errorDetails, setErrorDetails] = useState<string | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();
  const { updateUserEmail } = useAuth();

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Get request token and status from URL
        const request_token = searchParams.get('request_token');
        const status = searchParams.get('status');
        const redirect_params = searchParams.get('redirect_params');

        console.log('Callback received:', { request_token, status, redirect_params });

        // Check if this is a child OAuth flow
        if (redirect_params) {
          try {
            const params = JSON.parse(decodeURIComponent(redirect_params));
            if (params.flow_type === 'child_oauth') {
              console.log('Detected child OAuth flow, redirecting to child callback');
              // Redirect to child-specific callback with all parameters
              const childCallbackUrl = `/auth/zerodha/child-callback?request_token=${request_token}&status=${status}&redirect_params=${redirect_params}`;
              router.push(childCallbackUrl);
              return;
            }
          } catch (e) {
            console.error('Failed to parse redirect_params:', e);
          }
        }

        if (status === 'success' && request_token) {
          try {
            // Exchange request token for access token as per Zerodha docs
            const response = await axios.post('/api/zerodha', {
              request_token
            });

            if (response.data.success) {
              console.log('Token exchange successful');

              // Get user from localStorage
              const userStr = localStorage.getItem('user');
              if (!userStr) {
                throw new Error('User not found in localStorage');
              }

              const user = JSON.parse(userStr);

              // Store access token temporarily to fetch profile
              localStorage.setItem('zerodha_access_token', response.data.access_token);

              // Fetch user profile from Zerodha to get email
              let zerodhaEmail = user.email; // fallback to existing email
              try {
                const { ZerodhaApi } = await import('@/app/utils/zerodhaApi');
                const zerodhaApi = new ZerodhaApi();
                const profile = await zerodhaApi.getProfile();
                console.log('Zerodha profile:', profile);

                if (profile && profile.email) {
                  zerodhaEmail = profile.email;
                  console.log('Updated email from Zerodha:', zerodhaEmail);

                  // Update the user's email in the AuthContext
                  updateUserEmail(zerodhaEmail);
                }
              } catch (profileError) {
                console.error('Failed to fetch Zerodha profile, using existing email:', profileError);
              }

              // Update user with access token and email from Zerodha
              const updatedUser = {
                ...user,
                email: zerodhaEmail, // Update email with Zerodha email
                zerodhaAccessToken: response.data.access_token,
              };

              // Store updated user in localStorage
              localStorage.setItem('user', JSON.stringify(updatedUser));

              // Store public token if available
              if (response.data.public_token) {
                localStorage.setItem('zerodha_public_token', response.data.public_token);
              }

              // Parse redirect_params if available
              if (redirect_params) {
                try {
                  const params = JSON.parse(decodeURIComponent(redirect_params));
                  console.log('Redirect params:', params);
                  // You can use these params if needed
                } catch (e) {
                  console.error('Failed to parse redirect_params:', e);
                }
              }

              setStatus('success');
              setMessage('Zerodha account connected successfully!');
            } else {
              throw new Error('Failed to exchange token');
            }
          } catch (apiError: any) {
            console.error('API error:', apiError);
            setStatus('error');
            setMessage('Failed to exchange token with Zerodha API.');
            setErrorDetails(apiError.response?.data?.error || apiError.message);
          }
        } else {
          setStatus('error');
          setMessage(status === 'cancelled'
            ? 'Authentication was cancelled by the user.'
            : 'No request token found in the URL or authentication failed.');
        }
      } catch (error: any) {
        console.error('Zerodha callback error:', error);
        setStatus('error');
        setMessage('Failed to connect Zerodha account. Please try again.');
        setErrorDetails(error.message);
      }
    };

    processCallback();
  }, [searchParams]);

  const handleContinue = () => {
    const userRole = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).role : 'master';
    router.push(userRole === 'master' ? '/master/dashboard' : '/child/dashboard');
  };

  const handleRetry = () => {
    // Get API key from environment variable
    const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;

    if (!apiKey) {
      console.error('Zerodha API key not found in environment variables');
      return;
    }

    // Redirect to Zerodha login page
    window.location.href = `https://kite.zerodha.com/connect/login?v=3&api_key=${apiKey}`;
  };

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
      <h1 className="text-2xl font-bold mb-6">Zerodha Authentication</h1>

      {status === 'loading' && (
        <div className="flex justify-center mb-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      )}

      {status === 'success' && (
        <div className="text-green-600 text-5xl mb-4">✓</div>
      )}

      {status === 'error' && (
        <div className="text-red-600 text-5xl mb-4">✗</div>
      )}

      <p className="mb-4">{message}</p>

      {errorDetails && status === 'error' && (
        <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded mb-6 text-sm overflow-auto max-h-40 text-left">
          <p className="font-bold mb-1">Error Details:</p>
          <p className="whitespace-pre-wrap">{errorDetails}</p>
        </div>
      )}

      {status === 'success' && (
        <div className="mt-4">
          <p className="text-sm text-gray-600 mb-6">
            Your Zerodha account has been successfully connected. The access token has been stored and can now be used for placing orders.
          </p>
          <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded mb-6 text-sm text-left">
            <p className="font-bold mb-1">Access Token:</p>
            <p className="font-mono text-xs break-all">{localStorage.getItem('zerodha_access_token')}</p>
          </div>
          <Button onClick={handleContinue}>
            Continue to Dashboard
          </Button>
        </div>
      )}

      {status === 'error' && (
        <div className="mt-4">
          <Button onClick={handleRetry}>
            Try Again
          </Button>
          <Button onClick={handleContinue} className="ml-2 bg-gray-200 text-gray-800 hover:bg-gray-300">
            Return to Dashboard
          </Button>
        </div>
      )}
    </div>
  );
}

// Export the page component with Suspense
export default function ZerodhaCallbackPage() {
  return (
    <Suspense fallback={<LoadingCallback />}>
      <ZerodhaCallbackContent />
    </Suspense>
  );
}