'use client';

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from '../../../components/ui/accordion';
import { Badge } from '../../../components/ui/badge';

const faqs = [
  {
    question: "What is CopyTrade and how does it work?",
    answer: "CopyTrade is an AI-powered trading platform that allows you to automatically copy trades from Master accounts to Child accounts. When a Master trader executes a trade, our system instantly replicates it across all connected Child accounts with customizable risk parameters and position sizing."
  },
  {
    question: "Which brokers are supported?",
    answer: "Currently, we support Zerodha Kite Connect API with plans to expand to other major Indian brokers. Our secure API integration ensures your trading data remains protected while enabling seamless trade execution across all connected accounts."
  },
  {
    question: "How fast is the trade copying?",
    answer: "Our system executes trade copies within milliseconds of the original trade. We use advanced algorithms and direct API connections to ensure minimal latency, giving you the best possible execution prices for copied trades."
  },
  {
    question: "Is my trading data secure?",
    answer: "Yes, we use bank-level security with enterprise-grade encryption. Your API keys and trading data are stored securely, and we never have access to your funds. All communications are encrypted, and we follow strict security protocols to protect your information."
  },
  {
    question: "Can I customize risk management for Child accounts?",
    answer: "Absolutely! You can set individual risk parameters for each Child account, including position sizing, maximum exposure limits, stop-loss levels, and trading hours. Our AI-powered risk management system helps optimize these settings based on account size and risk tolerance."
  },
  {
    question: "What happens if the Master account loses money?",
    answer: "Child accounts will mirror the Master account's performance, including losses. However, our risk management tools allow you to set maximum loss limits, stop-loss orders, and other protective measures to help minimize potential losses."
  },
  {
    question: "How much does it cost?",
    answer: "We offer a free plan for getting started with basic features, a Pro plan at $29/month for serious traders, and an Enterprise plan at $99/month for large operations. All plans include our core trading features with different limits and advanced capabilities."
  },
  {
    question: "Can I try it before committing?",
    answer: "Yes! We offer a 30-day free trial for our Pro plan, and our Free plan is available forever with no credit card required. You can test all features and see how our platform works with your trading strategy before upgrading."
  },
  {
    question: "Do you provide customer support?",
    answer: "We provide email support for all users, priority support for Pro users, and 24/7 phone support for Enterprise customers. Our support team consists of experienced traders and technical experts who understand both trading and our platform."
  },
  {
    question: "Can I use this for different trading strategies?",
    answer: "Yes, our platform supports various trading strategies including day trading, swing trading, options trading, and more. You can set up different Master accounts for different strategies and connect specific Child accounts to each strategy."
  }
];

export default function FAQSection() {
  return (
    <section className="py-24 bg-white dark:bg-gray-950">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            FAQ
          </Badge>
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100 sm:text-4xl lg:text-5xl">
            Frequently asked{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              questions
            </span>
          </h2>
          <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600 dark:text-gray-300">
            Everything you need to know about CopyTrade. Can't find the answer you're looking for?
            Reach out to our support team.
          </p>
        </div>

        {/* FAQ Accordion */}
        <Accordion type="single" collapsible className="space-y-4">
          {faqs.map((faq, index) => (
            <AccordionItem
              key={index}
              value={`item-${index}`}
              className="border border-gray-200 dark:border-gray-700 rounded-lg px-6 hover:shadow-md dark:hover:shadow-gray-800/50 transition-shadow bg-white dark:bg-gray-900"
            >
              <AccordionTrigger className="text-left font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 py-6">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-gray-600 dark:text-gray-300 leading-relaxed pb-6">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>

        {/* Contact support */}
        <div className="mt-16 text-center">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our support team is here to help you get the most out of CopyTrade.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Badge variant="outline" className="px-4 py-2">
                📧 <EMAIL>
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                💬 Live chat available
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                📞 24/7 Enterprise support
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
